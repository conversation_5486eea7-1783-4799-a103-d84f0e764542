using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using UNI.Utilities.MinIo;
using UNI.Utilities.MinIo.Extensions;

namespace UNI.Utilities.MinIo.Examples
{
    /// <summary>
    /// Example usage of MinIO service
    /// </summary>
    public class MinIoServiceExample
    {
        private readonly IMinIoService _minIoService;
        private readonly ILogger<MinIoServiceExample> _logger;

        public MinIoServiceExample(IMinIoService minIoService, ILogger<MinIoServiceExample> logger)
        {
            _minIoService = minIoService;
            _logger = logger;
        }

        /// <summary>
        /// Demonstrates basic MinIO operations
        /// </summary>
        public async Task RunExampleAsync()
        {
            const string bucketName = "example-bucket";
            const string objectName = "example-file.txt";
            const string fileContent = "Hello, MinIO!";

            try
            {
                // 1. Create bucket if it doesn't exist
                await EnsureBucketExistsAsync(bucketName);

                // 2. Upload a text file
                await UploadTextFileAsync(bucketName, objectName, fileContent);

                // 3. Download and verify the file
                await DownloadAndVerifyFileAsync(bucketName, objectName, fileContent);

                // 4. Get object information
                await GetObjectInfoAsync(bucketName, objectName);

                // 5. List objects in bucket
                await ListObjectsAsync(bucketName);

                // 6. Generate presigned URLs
                await GeneratePresignedUrlsAsync(bucketName, objectName);

                // 7. Clean up - delete the object
                await DeleteObjectAsync(bucketName, objectName);

                _logger.LogInformation("MinIO example completed successfully!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running MinIO example");
                throw;
            }
        }

        private async Task EnsureBucketExistsAsync(string bucketName)
        {
            _logger.LogInformation("Checking if bucket '{BucketName}' exists...", bucketName);
            
            var exists = await _minIoService.BucketExistsAsync(bucketName);
            if (!exists)
            {
                _logger.LogInformation("Creating bucket '{BucketName}'...", bucketName);
                await _minIoService.CreateBucketAsync(bucketName);
                _logger.LogInformation("Bucket '{BucketName}' created successfully", bucketName);
            }
            else
            {
                _logger.LogInformation("Bucket '{BucketName}' already exists", bucketName);
            }
        }

        private async Task UploadTextFileAsync(string bucketName, string objectName, string content)
        {
            _logger.LogInformation("Uploading object '{ObjectName}' to bucket '{BucketName}'...", objectName, bucketName);
            
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(content));
            var result = await _minIoService.UploadObjectAsync(
                bucketName, 
                objectName, 
                stream, 
                contentType: "text/plain");

            _logger.LogInformation("Upload successful! ETag: {ETag}, Size: {Size} bytes", 
                result.ETag, result.Size);
        }

        private async Task DownloadAndVerifyFileAsync(string bucketName, string objectName, string expectedContent)
        {
            _logger.LogInformation("Downloading object '{ObjectName}' from bucket '{BucketName}'...", objectName, bucketName);
            
            var downloadedBytes = await _minIoService.GetObjectBytesAsync(bucketName, objectName);
            var downloadedContent = Encoding.UTF8.GetString(downloadedBytes);

            if (downloadedContent == expectedContent)
            {
                _logger.LogInformation("Download successful! Content verified: '{Content}'", downloadedContent);
            }
            else
            {
                _logger.LogWarning("Content mismatch! Expected: '{Expected}', Got: '{Actual}'", 
                    expectedContent, downloadedContent);
            }
        }

        private async Task GetObjectInfoAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Getting object info for '{ObjectName}' in bucket '{BucketName}'...", objectName, bucketName);
            
            var objectInfo = await _minIoService.GetObjectInfoAsync(bucketName, objectName);
            
            _logger.LogInformation("Object Info - Size: {Size} bytes, Last Modified: {LastModified}, ETag: {ETag}, Content Type: {ContentType}",
                objectInfo.Size, objectInfo.LastModified, objectInfo.ETag, objectInfo.ContentType);
        }

        private async Task ListObjectsAsync(string bucketName)
        {
            _logger.LogInformation("Listing objects in bucket '{BucketName}'...", bucketName);
            
            var objects = await _minIoService.ListObjectsAsync(bucketName);
            
            foreach (var obj in objects)
            {
                _logger.LogInformation("Object: {ObjectName}, Size: {Size} bytes, Last Modified: {LastModified}",
                    obj.ObjectName, obj.Size, obj.LastModified);
            }
        }

        private async Task GeneratePresignedUrlsAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Generating presigned URLs for object '{ObjectName}' in bucket '{BucketName}'...", objectName, bucketName);
            
            // Generate download URL (valid for 1 hour)
            var downloadUrl = await _minIoService.GetPresignedDownloadUrlAsync(
                bucketName, objectName, TimeSpan.FromHours(1));
            _logger.LogInformation("Download URL: {DownloadUrl}", downloadUrl);

            // Generate upload URL (valid for 1 hour)
            var uploadUrl = await _minIoService.GetPresignedUploadUrlAsync(
                bucketName, "new-" + objectName, TimeSpan.FromHours(1));
            _logger.LogInformation("Upload URL: {UploadUrl}", uploadUrl);
        }

        private async Task DeleteObjectAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Deleting object '{ObjectName}' from bucket '{BucketName}'...", objectName, bucketName);
            
            await _minIoService.DeleteObjectAsync(bucketName, objectName);
            
            _logger.LogInformation("Object '{ObjectName}' deleted successfully", objectName);
        }

        /// <summary>
        /// Example of how to set up dependency injection for MinIO service
        /// </summary>
        public static ServiceProvider CreateServiceProvider()
        {
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging();

            // Add MinIO service with settings
            services.AddMinIoService(settings =>
            {
                settings.Endpoint = "localhost:9000";
                settings.AccessKey = "minioadmin";
                settings.SecretKey = "minioadmin";
                settings.UseSSL = false;
                settings.CreateBucketIfNotExists = true;
            });

            // Add the example service
            services.AddTransient<MinIoServiceExample>();

            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Main entry point for running the example
        /// </summary>
        public static async Task Main(string[] args)
        {
            using var serviceProvider = CreateServiceProvider();
            var example = serviceProvider.GetRequiredService<MinIoServiceExample>();
            
            await example.RunExampleAsync();
        }
    }
}
