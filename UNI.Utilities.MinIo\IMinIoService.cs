using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace UNI.Utilities.MinIo
{
    /// <summary>
    /// Interface for MinIO object storage operations
    /// </summary>
    public interface IMinIoService : IDisposable
    {
        #region Bucket Operations
        
        /// <summary>
        /// Check if a bucket exists
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if bucket exists, false otherwise</returns>
        Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a new bucket
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="location">Location/region for the bucket (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task CreateBucketAsync(string bucketName, string? location = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a bucket
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteBucketAsync(string bucketName, CancellationToken cancellationToken = default);

        /// <summary>
        /// List all buckets
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of bucket information</returns>
        Task<IEnumerable<BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Object Operations

        /// <summary>
        /// Upload an object from stream
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="stream">Stream containing the object data</param>
        /// <param name="size">Size of the object (optional, will be determined from stream if not provided)</param>
        /// <param name="contentType">Content type of the object (optional)</param>
        /// <param name="metadata">Additional metadata for the object (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result information</returns>
        Task<UploadResult> UploadObjectAsync(
            string bucketName,
            string objectName,
            Stream stream,
            long? size = null,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Upload an object from file path
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="filePath">Path to the file to upload</param>
        /// <param name="contentType">Content type of the object (optional)</param>
        /// <param name="metadata">Additional metadata for the object (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result information</returns>
        Task<UploadResult> UploadFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Download an object to stream
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="stream">Stream to write the object data to</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DownloadObjectAsync(
            string bucketName,
            string objectName,
            Stream stream,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Download an object to file
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="filePath">Path where to save the downloaded file</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DownloadFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get object as byte array
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Object data as byte array</returns>
        Task<byte[]> GetObjectBytesAsync(
            string bucketName,
            string objectName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Check if an object exists
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if object exists, false otherwise</returns>
        Task<bool> ObjectExistsAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete an object
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteObjectAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete multiple objects
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectNames">List of object names to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of deletion results</returns>
        Task<IEnumerable<DeleteResult>> DeleteObjectsAsync(
            string bucketName,
            IEnumerable<string> objectNames,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get object information/metadata
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Object information</returns>
        Task<ObjectInfo> GetObjectInfoAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// List objects in a bucket
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="prefix">Prefix to filter objects (optional)</param>
        /// <param name="recursive">Whether to list objects recursively (optional, default: false)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of object information</returns>
        Task<IEnumerable<ObjectInfo>> ListObjectsAsync(
            string bucketName,
            string? prefix = null,
            bool recursive = false,
            CancellationToken cancellationToken = default);

        #endregion

        #region URL Operations

        /// <summary>
        /// Generate a presigned URL for downloading an object
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="expiry">URL expiration time (default: 7 days)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Presigned URL for downloading</returns>
        Task<string> GetPresignedDownloadUrlAsync(
            string bucketName,
            string objectName,
            TimeSpan? expiry = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Generate a presigned URL for uploading an object
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="expiry">URL expiration time (default: 7 days)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Presigned URL for uploading</returns>
        Task<string> GetPresignedUploadUrlAsync(
            string bucketName,
            string objectName,
            TimeSpan? expiry = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Generate a preview URL for viewing an object in browser
        /// This is similar to download URL but with appropriate headers for inline viewing
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="expiry">URL expiration time (default: 7 days)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Presigned URL for previewing/viewing the object inline</returns>
        Task<string> GetPreviewUrlAsync(
            string bucketName,
            string objectName,
            TimeSpan? expiry = null,
            CancellationToken cancellationToken = default);

        #endregion
    }
}
