# UNI.Utilities.MinIo

A comprehensive .NET library for MinIO object storage operations, providing a clean and easy-to-use interface for all common MinIO operations.

## Features

- **Bucket Operations**: Create, delete, list, and check bucket existence
- **Object Operations**: Upload, download, delete, and get object information
- **Batch Operations**: Delete multiple objects at once
- **Presigned URLs**: Generate temporary URLs for upload and download
- **Comprehensive Logging**: Built-in logging support
- **Dependency Injection**: Easy integration with .NET DI container
- **Async/Await**: Full async support with cancellation tokens
- **Error Handling**: Custom exceptions with detailed error information

## Installation

Add the project reference to your application:

```xml
<ItemGroup>
  <ProjectReference Include="..\UNI.Utilities.MinIo\UNI.Utilities.MinIo.csproj" />
</ItemGroup>
```

## Configuration

### appsettings.json

```json
{
  "MinIo": {
    "Endpoint": "localhost:9000",
    "AccessKey": "your-access-key",
    "SecretKey": "your-secret-key",
    "UseSSL": false,
    "Region": "us-east-1",
    "DefaultBucket": "my-bucket",
    "TimeoutSeconds": 30,
    "CreateBucketIfNotExists": true
  }
}
```

### Dependency Injection Setup

```csharp
using UNI.Utilities.MinIo.Extensions;

public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Option 1: Using configuration section
        services.AddMinIoService(Configuration.GetSection("MinIo"));
        
        // Option 2: Using settings object
        services.AddMinIoService(new MinIoSettings
        {
            Endpoint = "localhost:9000",
            AccessKey = "your-access-key",
            SecretKey = "your-secret-key",
            UseSSL = false
        });
        
        // Option 3: Using configuration action
        services.AddMinIoService(settings =>
        {
            settings.Endpoint = "localhost:9000";
            settings.AccessKey = "your-access-key";
            settings.SecretKey = "your-secret-key";
            settings.UseSSL = false;
        });
    }
}
```

## Usage Examples

### Basic Operations

```csharp
public class FileService
{
    private readonly IMinIoService _minIoService;
    
    public FileService(IMinIoService minIoService)
    {
        _minIoService = minIoService;
    }
    
    // Upload a file
    public async Task<UploadResult> UploadFileAsync(string filePath, string bucketName, string objectName)
    {
        return await _minIoService.UploadFileAsync(bucketName, objectName, filePath);
    }
    
    // Upload from stream
    public async Task<UploadResult> UploadStreamAsync(Stream stream, string bucketName, string objectName)
    {
        return await _minIoService.UploadObjectAsync(bucketName, objectName, stream);
    }
    
    // Download to file
    public async Task DownloadFileAsync(string bucketName, string objectName, string filePath)
    {
        await _minIoService.DownloadFileAsync(bucketName, objectName, filePath);
    }
    
    // Download as byte array
    public async Task<byte[]> DownloadBytesAsync(string bucketName, string objectName)
    {
        return await _minIoService.GetObjectBytesAsync(bucketName, objectName);
    }
    
    // Check if object exists
    public async Task<bool> FileExistsAsync(string bucketName, string objectName)
    {
        return await _minIoService.ObjectExistsAsync(bucketName, objectName);
    }
    
    // Delete object
    public async Task DeleteFileAsync(string bucketName, string objectName)
    {
        await _minIoService.DeleteObjectAsync(bucketName, objectName);
    }
}
```

### Bucket Management

```csharp
// Create bucket
await _minIoService.CreateBucketAsync("my-bucket");

// Check if bucket exists
var exists = await _minIoService.BucketExistsAsync("my-bucket");

// List all buckets
var buckets = await _minIoService.ListBucketsAsync();

// Delete bucket
await _minIoService.DeleteBucketAsync("my-bucket");
```

### Object Listing and Information

```csharp
// List all objects in bucket
var objects = await _minIoService.ListObjectsAsync("my-bucket");

// List objects with prefix
var filteredObjects = await _minIoService.ListObjectsAsync("my-bucket", "documents/");

// List objects recursively
var allObjects = await _minIoService.ListObjectsAsync("my-bucket", recursive: true);

// Get object information
var objectInfo = await _minIoService.GetObjectInfoAsync("my-bucket", "my-file.pdf");
```

### Presigned URLs

```csharp
// Generate download URL (valid for 7 days by default)
var downloadUrl = await _minIoService.GetPresignedDownloadUrlAsync("my-bucket", "my-file.pdf");

// Generate upload URL with custom expiry
var uploadUrl = await _minIoService.GetPresignedUploadUrlAsync(
    "my-bucket", 
    "new-file.pdf", 
    TimeSpan.FromHours(1));
```

### Batch Operations

```csharp
// Delete multiple objects
var objectsToDelete = new[] { "file1.pdf", "file2.pdf", "file3.pdf" };
var deleteResults = await _minIoService.DeleteObjectsAsync("my-bucket", objectsToDelete);

foreach (var result in deleteResults)
{
    if (result.IsSuccess)
        Console.WriteLine($"Successfully deleted {result.ObjectName}");
    else
        Console.WriteLine($"Failed to delete {result.ObjectName}: {result.ErrorMessage}");
}
```

### Error Handling

```csharp
try
{
    await _minIoService.UploadFileAsync("my-bucket", "my-file.pdf", "/path/to/file.pdf");
}
catch (MinIoException ex)
{
    Console.WriteLine($"MinIO Error: {ex.Message}");
    Console.WriteLine($"Error Code: {ex.ErrorCode}");
    Console.WriteLine($"Bucket: {ex.BucketName}");
    Console.WriteLine($"Object: {ex.ObjectName}");
}
catch (FileNotFoundException ex)
{
    Console.WriteLine($"File not found: {ex.Message}");
}
```

## API Reference

### IMinIoService Interface

The main interface provides the following method categories:

- **Bucket Operations**: `BucketExistsAsync`, `CreateBucketAsync`, `DeleteBucketAsync`, `ListBucketsAsync`
- **Object Operations**: `UploadObjectAsync`, `UploadFileAsync`, `DownloadObjectAsync`, `DownloadFileAsync`, `GetObjectBytesAsync`, `ObjectExistsAsync`, `DeleteObjectAsync`, `DeleteObjectsAsync`, `GetObjectInfoAsync`, `ListObjectsAsync`
- **URL Operations**: `GetPresignedDownloadUrlAsync`, `GetPresignedUploadUrlAsync`

### Models

- **BucketInfo**: Information about a bucket
- **ObjectInfo**: Information about an object
- **UploadResult**: Result of an upload operation
- **DeleteResult**: Result of a delete operation
- **MinIoSettings**: Configuration settings
- **MinIoException**: Custom exception for MinIO operations

## License

This project is part of the UNI.Utilities suite.
