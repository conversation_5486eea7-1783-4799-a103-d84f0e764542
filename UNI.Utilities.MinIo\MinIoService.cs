using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using Minio.Exceptions;

namespace UNI.Utilities.MinIo
{
    /// <summary>
    /// Implementation of MinIO object storage service
    /// </summary>
    public class MinIoService : IMinIoService
    {
        private readonly IMinioClient _minioClient;
        private readonly MinIoSettings _settings;
        private readonly ILogger<MinIoService> _logger;

        /// <summary>
        /// Constructor for MinIoService
        /// </summary>
        /// <param name="settings">MinIO configuration settings</param>
        /// <param name="logger">Logger instance</param>
        public MinIoService(IOptions<MinIoSettings> settings, ILogger<MinIoService> logger)
        {
            _settings = settings.Value ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            ValidateSettings();
            _minioClient = CreateMinioClient();
        }

        /// <summary>
        /// Constructor for MinIoService with direct settings
        /// </summary>
        /// <param name="settings">MinIO configuration settings</param>
        /// <param name="logger">Logger instance</param>
        public MinIoService(MinIoSettings settings, ILogger<MinIoService> logger)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            ValidateSettings();
            _minioClient = CreateMinioClient();
        }

        #region Private Methods

        private void ValidateSettings()
        {
            if (string.IsNullOrWhiteSpace(_settings.Endpoint))
                throw new ArgumentException("Endpoint is required", nameof(_settings.Endpoint));

            if (string.IsNullOrWhiteSpace(_settings.AccessKey))
                throw new ArgumentException("AccessKey is required", nameof(_settings.AccessKey));

            if (string.IsNullOrWhiteSpace(_settings.SecretKey))
                throw new ArgumentException("SecretKey is required", nameof(_settings.SecretKey));
        }

        private IMinioClient CreateMinioClient()
        {
            var clientBuilder = new MinioClient()
                .WithEndpoint(_settings.Endpoint)
                .WithCredentials(_settings.AccessKey, _settings.SecretKey)
                .WithTimeout(_settings.TimeoutSeconds * 1000);

            if (_settings.UseSSL)
            {
                clientBuilder = clientBuilder.WithSSL();
            }

            if (!string.IsNullOrWhiteSpace(_settings.Region))
            {
                clientBuilder = clientBuilder.WithRegion(_settings.Region);
            }

            return clientBuilder.Build();
        }

        private async Task EnsureBucketExistsAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            if (_settings.CreateBucketIfNotExists)
            {
                var bucketExists = await BucketExistsAsync(bucketName, cancellationToken);
                if (!bucketExists)
                {
                    await CreateBucketAsync(bucketName, _settings.Region, cancellationToken);
                }
            }
        }

        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName)?.ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".pdf" => "application/pdf",
                ".txt" => "text/plain",
                ".html" => "text/html",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                ".xml" => "application/xml",
                ".zip" => "application/zip",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                _ => "application/octet-stream"
            };
        }

        private MinIoException CreateMinIoException(Exception ex, string operation, string? bucketName = null, string? objectName = null)
        {
            var message = $"MinIO operation '{operation}' failed: {ex.Message}";

            if (ex is MinioException minioEx)
            {
                return new MinIoException(message, minioEx.ServerMessage, bucketName, objectName);
            }

            return new MinIoException(message, null, bucketName, objectName);
        }

        private string ReplaceEndpointInUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(_settings.ProxyEndpoint))
            {
                return url;
            }

            try
            {
                var uri = new Uri(url);
                var proxyUri = new Uri(_settings.ProxyEndpoint);

                // Replace the host and scheme from the original URL with the proxy endpoint
                var builder = new UriBuilder(uri)
                {
                    Scheme = proxyUri.Scheme,
                    Host = proxyUri.Host,
                    Port = proxyUri.Port
                };

                var result = builder.ToString();
                _logger.LogDebug("Replaced endpoint in URL: {OriginalUrl} -> {ProxyUrl}", url, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to replace endpoint in URL {Url} with proxy {ProxyEndpoint}, returning original URL",
                    url, _settings.ProxyEndpoint);
                return url;
            }
        }

        private static void ValidatePartSize(long partSize, long fileSize)
        {
            const long minPartSize = 5 * 1024 * 1024; // 5MB
            const long maxPartSize = 5L * 1024 * 1024 * 1024; // 5GB
            const int maxParts = 10000;

            if (partSize < minPartSize)
            {
                throw new ArgumentException($"Part size must be at least {minPartSize:N0} bytes (5MB)", nameof(partSize));
            }

            if (partSize > maxPartSize)
            {
                throw new ArgumentException($"Part size cannot exceed {maxPartSize:N0} bytes (5GB)", nameof(partSize));
            }

            var totalParts = (int)Math.Ceiling((double)fileSize / partSize);
            if (totalParts > maxParts)
            {
                var recommendedPartSize = (long)Math.Ceiling((double)fileSize / maxParts);
                throw new ArgumentException($"File size {fileSize:N0} bytes with part size {partSize:N0} bytes would require {totalParts} parts, " +
                    $"which exceeds the maximum of {maxParts} parts. Consider using a part size of at least {recommendedPartSize:N0} bytes.",
                    nameof(partSize));
            }
        }

        #endregion

        #region Bucket Operations

        public async Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Checking if bucket {BucketName} exists", bucketName);
                
                var args = new BucketExistsArgs()
                    .WithBucket(bucketName);

                var result = await _minioClient.BucketExistsAsync(args, cancellationToken);
                
                _logger.LogDebug("Bucket {BucketName} exists: {Exists}", bucketName, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if bucket {BucketName} exists", bucketName);
                throw CreateMinIoException(ex, "BucketExists", bucketName);
            }
        }

        public async Task CreateBucketAsync(string bucketName, string? location = null, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating bucket {BucketName} in location {Location}", bucketName, location ?? "default");
                
                var args = new MakeBucketArgs()
                    .WithBucket(bucketName);

                if (!string.IsNullOrWhiteSpace(location))
                {
                    args = args.WithLocation(location);
                }

                await _minioClient.MakeBucketAsync(args, cancellationToken);
                
                _logger.LogInformation("Successfully created bucket {BucketName}", bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bucket {BucketName}", bucketName);
                throw CreateMinIoException(ex, "CreateBucket", bucketName);
            }
        }

        public async Task DeleteBucketAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting bucket {BucketName}", bucketName);
                
                var args = new RemoveBucketArgs()
                    .WithBucket(bucketName);

                await _minioClient.RemoveBucketAsync(args, cancellationToken);
                
                _logger.LogInformation("Successfully deleted bucket {BucketName}", bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting bucket {BucketName}", bucketName);
                throw CreateMinIoException(ex, "DeleteBucket", bucketName);
            }
        }

        public async Task<IEnumerable<BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Listing all buckets");
                
                var buckets = await _minioClient.ListBucketsAsync(cancellationToken);
                
                var result = buckets.Buckets.Select(b => new BucketInfo
                {
                    Name = b.Name,
                    CreationDate = b.CreationDateDateTime
                }).ToList();
                
                _logger.LogDebug("Found {Count} buckets", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing buckets");
                throw CreateMinIoException(ex, "ListBuckets");
            }
        }

        #endregion

        #region Object Operations

        public async Task<UploadResult> UploadObjectAsync(
            string bucketName,
            string objectName,
            Stream stream,
            long? size = null,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Uploading object {ObjectName} to bucket {BucketName}", objectName, bucketName);

                await EnsureBucketExistsAsync(bucketName, cancellationToken);

                var objectSize = size ?? stream.Length;
                var detectedContentType = contentType ?? GetContentType(objectName);

                var args = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithStreamData(stream)
                    .WithObjectSize(objectSize)
                    .WithContentType(detectedContentType);

                if (metadata != null && metadata.Any())
                {
                    args = args.WithHeaders(metadata);
                }

                var response = await _minioClient.PutObjectAsync(args, cancellationToken);

                _logger.LogInformation("Successfully uploaded object {ObjectName} to bucket {BucketName}, ETag: {ETag}",
                    objectName, bucketName, response.Etag);

                return new UploadResult
                {
                    BucketName = bucketName,
                    ObjectName = objectName,
                    ETag = response.Etag,
                    Size = objectSize,
                    ContentType = detectedContentType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading object {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw CreateMinIoException(ex, "UploadObject", bucketName, objectName);
            }
        }

        public async Task<UploadResult> UploadFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Uploading file {FilePath} as object {ObjectName} to bucket {BucketName}",
                    filePath, objectName, bucketName);

                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var detectedContentType = contentType ?? GetContentType(filePath);

                return await UploadObjectAsync(bucketName, objectName, fileStream, fileStream.Length,
                    detectedContentType, metadata, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FilePath} as object {ObjectName} to bucket {BucketName}",
                    filePath, objectName, bucketName);
                throw CreateMinIoException(ex, "UploadFile", bucketName, objectName);
            }
        }

        public async Task DownloadObjectAsync(
            string bucketName,
            string objectName,
            Stream stream,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Downloading object {ObjectName} from bucket {BucketName}", objectName, bucketName);

                var args = new GetObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithCallbackStream(async (downloadStream, ct) =>
                    {
                        await downloadStream.CopyToAsync(stream, ct);
                    });

                await _minioClient.GetObjectAsync(args, cancellationToken);

                _logger.LogDebug("Successfully downloaded object {ObjectName} from bucket {BucketName}", objectName, bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw CreateMinIoException(ex, "DownloadObject", bucketName, objectName);
            }
        }

        public async Task DownloadFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Downloading object {ObjectName} from bucket {BucketName} to file {FilePath}",
                    objectName, bucketName, filePath);

                // Ensure directory exists
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
                await DownloadObjectAsync(bucketName, objectName, fileStream, cancellationToken);

                _logger.LogInformation("Successfully downloaded object {ObjectName} from bucket {BucketName} to file {FilePath}",
                    objectName, bucketName, filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading object {ObjectName} from bucket {BucketName} to file {FilePath}",
                    objectName, bucketName, filePath);
                throw CreateMinIoException(ex, "DownloadFile", bucketName, objectName);
            }
        }

        public async Task<byte[]> GetObjectBytesAsync(
            string bucketName,
            string objectName,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting object {ObjectName} from bucket {BucketName} as byte array", objectName, bucketName);

                using var memoryStream = new MemoryStream();
                await DownloadObjectAsync(bucketName, objectName, memoryStream, cancellationToken);

                var result = memoryStream.ToArray();
                _logger.LogDebug("Successfully retrieved object {ObjectName} from bucket {BucketName}, size: {Size} bytes",
                    objectName, bucketName, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object {ObjectName} from bucket {BucketName} as byte array", objectName, bucketName);
                throw CreateMinIoException(ex, "GetObjectBytes", bucketName, objectName);
            }
        }

        public async Task<UploadResult> UploadLargeFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            long partSize = 64 * 1024 * 1024,
            IProgress<UploadProgress>? progressCallback = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Uploading large file {FilePath} as object {ObjectName} to bucket {BucketName} with part size {PartSize}",
                    filePath, objectName, bucketName, partSize);

                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;
                var detectedContentType = contentType ?? GetContentType(filePath);

                // Validate part size
                ValidatePartSize(partSize, fileSize);

                await EnsureBucketExistsAsync(bucketName, cancellationToken);

                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                return await UploadLargeObjectAsync(bucketName, objectName, fileStream, fileSize,
                    detectedContentType, metadata, partSize, progressCallback, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading large file {FilePath} as object {ObjectName} to bucket {BucketName}",
                    filePath, objectName, bucketName);
                throw CreateMinIoException(ex, "UploadLargeFile", bucketName, objectName);
            }
        }

        public async Task<UploadResult> UploadLargeObjectAsync(
            string bucketName,
            string objectName,
            Stream stream,
            long size,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            long partSize = 64 * 1024 * 1024,
            IProgress<UploadProgress>? progressCallback = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Uploading large object {ObjectName} to bucket {BucketName}, size: {Size} bytes, part size: {PartSize}",
                    objectName, bucketName, size, partSize);

                // Validate part size
                ValidatePartSize(partSize, size);

                await EnsureBucketExistsAsync(bucketName, cancellationToken);

                var detectedContentType = contentType ?? "application/octet-stream";
                var totalParts = (int)Math.Ceiling((double)size / partSize);

                // Initialize progress
                var progress = new UploadProgress
                {
                    TotalBytes = size,
                    UploadedBytes = 0,
                    TotalParts = totalParts,
                    CurrentPart = 0,
                    IsMultipartUpload = true,
                    Status = "Starting upload"
                };

                progressCallback?.Report(progress);

                var startTime = DateTime.UtcNow;

                // Use MinIO's built-in multipart upload
                var args = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithStreamData(stream)
                    .WithObjectSize(size)
                    .WithContentType(detectedContentType);

                if (metadata != null && metadata.Count > 0)
                {
                    args = args.WithHeaders(metadata);
                }

                // Create a progress-tracking stream wrapper
                var progressStream = new ProgressTrackingStream(stream, size, progressCallback, startTime);

                args = args.WithStreamData(progressStream).WithObjectSize(size);

                var response = await _minioClient.PutObjectAsync(args, cancellationToken);

                // Final progress update
                progress.UploadedBytes = size;
                progress.Status = "Upload completed";
                progressCallback?.Report(progress);

                _logger.LogInformation("Successfully uploaded large object {ObjectName} to bucket {BucketName}, size: {Size} bytes, ETag: {ETag}",
                    objectName, bucketName, size, response.Etag);

                return new UploadResult
                {
                    BucketName = bucketName,
                    ObjectName = objectName,
                    ETag = response.Etag,
                    Size = size,
                    ContentType = detectedContentType,
                    IsMultipartUpload = true,
                    PartCount = totalParts
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading large object {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw CreateMinIoException(ex, "UploadLargeObject", bucketName, objectName);
            }
        }

        public async Task<bool> ObjectExistsAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Checking if object {ObjectName} exists in bucket {BucketName}", objectName, bucketName);

                var args = new StatObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName);

                await _minioClient.StatObjectAsync(args, cancellationToken);

                _logger.LogDebug("Object {ObjectName} exists in bucket {BucketName}", objectName, bucketName);
                return true;
            }
            catch (ObjectNotFoundException)
            {
                _logger.LogDebug("Object {ObjectName} does not exist in bucket {BucketName}", objectName, bucketName);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if object {ObjectName} exists in bucket {BucketName}", objectName, bucketName);
                throw CreateMinIoException(ex, "ObjectExists", bucketName, objectName);
            }
        }

        public async Task DeleteObjectAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting object {ObjectName} from bucket {BucketName}", objectName, bucketName);

                var args = new RemoveObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName);

                await _minioClient.RemoveObjectAsync(args, cancellationToken);

                _logger.LogInformation("Successfully deleted object {ObjectName} from bucket {BucketName}", objectName, bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw CreateMinIoException(ex, "DeleteObject", bucketName, objectName);
            }
        }

        public async Task<IEnumerable<DeleteResult>> DeleteObjectsAsync(
            string bucketName,
            IEnumerable<string> objectNames,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var objectNamesList = objectNames.ToList();
                _logger.LogDebug("Deleting {Count} objects from bucket {BucketName}", objectNamesList.Count, bucketName);

                var args = new RemoveObjectsArgs()
                    .WithBucket(bucketName)
                    .WithObjects(objectNamesList);

                var results = new List<DeleteResult>();
                var deleteErrors = await _minioClient.RemoveObjectsAsync(args, cancellationToken);

                foreach (var deleteError in deleteErrors)
                {
                    results.Add(new DeleteResult
                    {
                        ObjectName = deleteError.Key,
                        IsSuccess = false,
                        ErrorMessage = deleteError.Message
                    });
                }

                // Add successful deletions (objects not in error list)
                var errorObjectNames = results.Select(r => r.ObjectName).ToHashSet();
                foreach (var objectName in objectNamesList.Where(name => !errorObjectNames.Contains(name)))
                {
                    results.Add(new DeleteResult
                    {
                        ObjectName = objectName,
                        IsSuccess = true
                    });
                }

                _logger.LogInformation("Completed deletion of {Count} objects from bucket {BucketName}, {SuccessCount} successful, {ErrorCount} failed",
                    objectNamesList.Count, bucketName, results.Count(r => r.IsSuccess), results.Count(r => !r.IsSuccess));

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting objects from bucket {BucketName}", bucketName);
                throw CreateMinIoException(ex, "DeleteObjects", bucketName);
            }
        }

        public async Task<ObjectInfo> GetObjectInfoAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting object info for {ObjectName} in bucket {BucketName}", objectName, bucketName);

                var args = new StatObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName);

                var stat = await _minioClient.StatObjectAsync(args, cancellationToken);

                var result = new ObjectInfo
                {
                    ObjectName = stat.ObjectName,
                    BucketName = bucketName,
                    Size = stat.Size,
                    LastModified = stat.LastModified,
                    ETag = stat.ETag,
                    ContentType = stat.ContentType,
                    Metadata = stat.MetaData ?? new Dictionary<string, string>()
                };

                _logger.LogDebug("Successfully retrieved object info for {ObjectName} in bucket {BucketName}", objectName, bucketName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object info for {ObjectName} in bucket {BucketName}", objectName, bucketName);
                throw CreateMinIoException(ex, "GetObjectInfo", bucketName, objectName);
            }
        }

        public async Task<IEnumerable<ObjectInfo>> ListObjectsAsync(
            string bucketName,
            string? prefix = null,
            bool recursive = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Listing objects in bucket {BucketName} with prefix {Prefix}, recursive: {Recursive}",
                    bucketName, prefix ?? "none", recursive);

                var args = new ListObjectsArgs()
                    .WithBucket(bucketName)
                    .WithRecursive(recursive);

                if (!string.IsNullOrWhiteSpace(prefix))
                {
                    args = args.WithPrefix(prefix);
                }

                var results = new List<ObjectInfo>();
                var observable = _minioClient.ListObjectsEnumAsync(args, cancellationToken);

                await foreach (var item in observable)
                {
                    results.Add(new ObjectInfo
                    {
                        ObjectName = item.Key,
                        BucketName = bucketName,
                        Size = (long)item.Size,
                        LastModified = DateTime.TryParse(item.LastModified, out var lastModified) ? lastModified : DateTime.MinValue,
                        ETag = item.ETag,
                        IsDir = item.IsDir
                    });
                }

                _logger.LogDebug("Found {Count} objects in bucket {BucketName}", results.Count, bucketName);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing objects in bucket {BucketName}", bucketName);
                throw CreateMinIoException(ex, "ListObjects", bucketName);
            }
        }

        #endregion

        #region URL Operations

        public async Task<string> GetPresignedDownloadUrlAsync(
            string bucketName,
            string objectName,
            TimeSpan? expiry = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var expiryTime = expiry ?? TimeSpan.FromDays(7);
                _logger.LogDebug("Generating presigned download URL for object {ObjectName} in bucket {BucketName}, expiry: {Expiry}",
                    objectName, bucketName, expiryTime);

                var args = new PresignedGetObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithExpiry((int)expiryTime.TotalSeconds);

                var url = await _minioClient.PresignedGetObjectAsync(args);

                // Replace endpoint with proxy endpoint if configured
                var finalUrl = ReplaceEndpointInUrl(url);

                _logger.LogDebug("Successfully generated presigned download URL for object {ObjectName} in bucket {BucketName}",
                    objectName, bucketName);

                return finalUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned download URL for object {ObjectName} in bucket {BucketName}",
                    objectName, bucketName);
                throw CreateMinIoException(ex, "GetPresignedDownloadUrl", bucketName, objectName);
            }
        }

        public async Task<string> GetPresignedUploadUrlAsync(
            string bucketName,
            string objectName,
            TimeSpan? expiry = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var expiryTime = expiry ?? TimeSpan.FromDays(7);
                _logger.LogDebug("Generating presigned upload URL for object {ObjectName} in bucket {BucketName}, expiry: {Expiry}",
                    objectName, bucketName, expiryTime);

                var args = new PresignedPutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithExpiry((int)expiryTime.TotalSeconds);

                var url = await _minioClient.PresignedPutObjectAsync(args);

                // Replace endpoint with proxy endpoint if configured
                var finalUrl = ReplaceEndpointInUrl(url);

                _logger.LogDebug("Successfully generated presigned upload URL for object {ObjectName} in bucket {BucketName}",
                    objectName, bucketName);

                return finalUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned upload URL for object {ObjectName} in bucket {BucketName}",
                    objectName, bucketName);
                throw CreateMinIoException(ex, "GetPresignedUploadUrl", bucketName, objectName);
            }
        }

        public async Task<string> GetPreviewUrlAsync(
            string bucketName,
            string objectName,
            TimeSpan? expiry = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var expiryTime = expiry ?? TimeSpan.FromDays(7);
                _logger.LogDebug("Generating preview URL for object {ObjectName} in bucket {BucketName}, expiry: {Expiry}",
                    objectName, bucketName, expiryTime);

                // Create headers to force inline display instead of download
                var headers = new Dictionary<string, string>
                {
                    ["response-content-disposition"] = "inline"
                };

                // Get object info to determine content type for better preview experience
                try
                {
                    var objectInfo = await GetObjectInfoAsync(bucketName, objectName, cancellationToken);
                    if (!string.IsNullOrWhiteSpace(objectInfo.ContentType))
                    {
                        headers["response-content-type"] = objectInfo.ContentType;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not retrieve object info for preview URL, continuing without content-type header");
                }

                var args = new PresignedGetObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithExpiry((int)expiryTime.TotalSeconds)
                    .WithHeaders(headers);

                var url = await _minioClient.PresignedGetObjectAsync(args);

                // Replace endpoint with proxy endpoint if configured
                var finalUrl = ReplaceEndpointInUrl(url);

                _logger.LogDebug("Successfully generated preview URL for object {ObjectName} in bucket {BucketName}",
                    objectName, bucketName);

                return finalUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating preview URL for object {ObjectName} in bucket {BucketName}",
                    objectName, bucketName);
                throw CreateMinIoException(ex, "GetPreviewUrl", bucketName, objectName);
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            _minioClient?.Dispose();
        }

        #endregion
    }
}
